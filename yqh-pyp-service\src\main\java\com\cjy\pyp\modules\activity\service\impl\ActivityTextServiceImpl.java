package com.cjy.pyp.modules.activity.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Map;
import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.Query;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.common.utils.TextUtils;
import com.cjy.pyp.modules.activity.dao.ActivityTextDao;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.activity.service.ActivityTextPreGenerateService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.service.AdTypeConfigService;
import com.cjy.pyp.modules.activity.service.AiModelService;

@Service("activityTextService")
public class ActivityTextServiceImpl extends ServiceImpl<ActivityTextDao, ActivityTextEntity>
        implements ActivityTextService {

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private AdTypeConfigService adTypeConfigService;

    @Autowired
    private AiModelService aiModelService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ActivityTextPreGenerateService activityTextPreGenerateService;

    @Override
    public PageUtils queryPage(Map<String, Object> params) {
        QueryWrapper<ActivityTextEntity> wrapper = new QueryWrapper<>();

        // 按活动ID过滤
        String activityId = (String) params.get("activityId");
        wrapper.eq("activity_id", activityId);

        // 按广告类型过滤
        String adType = (String) params.get("adType");
        if (adType != null && !adType.trim().isEmpty()) {
            wrapper.eq("ad_type", adType);
        }

        // 按标题关键词过滤
        String name = (String) params.get("name");
        if (name != null && !name.trim().isEmpty()) {
            wrapper.like("name", name);
        }

        // 按时间排序
        wrapper.orderByDesc("create_on");

        IPage<ActivityTextEntity> page = this.page(
                new Query<ActivityTextEntity>().getPage(params),
                wrapper);

        return new PageUtils(page);
    }

    @Override
    public List<ActivityTextEntity> findByIds(List<Long> ids) {
        return ids.size() > 0 ? this.listByIds(ids) : new ArrayList<>();
    }

    @Override
    public ActivityTextEntity findUnusedByActivityId(Long activityId) {
        return this.getOne(new QueryWrapper<ActivityTextEntity>()
                .eq("activity_id", activityId)
                .eq("use_count", 0)
                .orderByAsc("create_on")
                .last("LIMIT 1"));
    }

    @Override
    public void incrementUseCount(Long textId) {
        ActivityTextEntity entity = this.getById(textId);
        if (entity != null) {
            entity.setUseCount(entity.getUseCount() == null ? 1 : entity.getUseCount() + 1);
            this.updateById(entity);
        }
    }

    @Override
    public ActivityTextEntity findByActivityIdAndAdType(Long activityId, String adType) throws Exception {
        QueryWrapper<ActivityTextEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        wrapper.eq("ad_type", adType);
        wrapper.orderByDesc("create_on"); // 按创建时间倒序，获取最新的
        List<ActivityTextEntity> activityTextEntities = this.list(wrapper);
        if (CollectionUtils.isEmpty(activityTextEntities)) {
            // 直接生成文案
            ActivityTextEntity activityText = new ActivityTextEntity();
            activityText.setActivityId(activityId);
            activityText.setAdType(adType);
            // 生成文案
            R result = this.generateText(activityText, null);
            if (!result.get("code").equals(200)) {
                throw new RuntimeException("生成文案失败");
            }
            return (ActivityTextEntity) result.get("activityText");
        } else {
            // 如果文案数量<=2 则直接后台生成
            if (activityTextEntities.size() <= 2) {
                activityTextPreGenerateService.preGenerateTextAsync(activityTextEntities.get(0), null);
            }
            return activityTextEntities.get(0);
        }
    }

    @Override
    public ActivityTextEntity findByActivityIdAndAdTypeNotUse(Long activityId, String adType) throws Exception {
        // 优先查找预生成的文案
        QueryWrapper<ActivityTextEntity> preWrapper = new QueryWrapper<>();
        preWrapper.eq("activity_id", activityId);
        preWrapper.eq("ad_type", adType);
        preWrapper.eq("use_count", 0);
        preWrapper.orderByAsc("create_on"); // 按创建时间正序，优先使用最早的预生成文案
        
        List<ActivityTextEntity> activityTextEntities = this.list(preWrapper);
        if (CollectionUtils.isEmpty(activityTextEntities)) {
            // 直接生成文案
            ActivityTextEntity activityText = new ActivityTextEntity();
            activityText.setActivityId(activityId);
            activityText.setAdType(adType);
            // 生成文案
            R result = this.generateText(activityText, null);
            if (!result.get("code").equals(200)) {
                throw new RuntimeException("生成文案失败");
            }
            return (ActivityTextEntity) result.get("activityText");
        } else {
            // 如果文案数量<=2 则直接后台生成
            if (activityTextEntities.size() <= 5) {
                activityTextPreGenerateService.preGenerateTextAsync(activityTextEntities.get(0), null);
            }
            return activityTextEntities.get(0);
        }
    }

    @Override
    public R generateText(ActivityTextEntity activityText, Long userId) throws Exception {
        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                userId,
                activityText.getActivityId(),
                1, // 生成文案类型
                null,
                "生成文案");

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }

        // 使用广告类型配置服务构建完整的Prompt
        String fullPrompt;
        if (StringUtils.isNotEmpty(activityText.getPrompt())) {
            // 如果已经有完整的prompt，直接使用
            fullPrompt = activityText.getPrompt();
        } else {
            // 否则根据广告类型配置构建prompt
            // 找到Activity
            ActivityEntity activity = activityService.getById(activityText.getActivityId());
            if (StringUtils.isEmpty(activityText.getAdType())) {
                activityText.setAdType("general");
            }
            if (StringUtils.isEmpty(activityText.getNameMode())) {
                activityText.setNameMode(activity.getNameMode());
            }
            if (StringUtils.isEmpty(activityText.getName())) {
                activityText.setName(activity.getDefaultName());
            }
            if (StringUtils.isEmpty(activityText.getQuery())) {
                activityText.setQuery(activity.getDefaultTitle());
            }
            // 如果用户自定义输入为空，使用活动的默认用户输入
            if (StringUtils.isEmpty(activityText.getUserCustomInput())) {
                activityText.setUserCustomInput(activity.getDefaultUserInput());
            }
            // 如果AI标签为空，使用活动的默认AI标签
            if (StringUtils.isEmpty(activityText.getAiTag())) {
                activityText.setAiTag(activity.getAiTag());
            }
            fullPrompt = adTypeConfigService.buildPrompt(activityText.getAdType(), activityText.getQuery(),
                    activityText.getNameMode(), activityText.getName(), activityText.getUserCustomInput(), activityText.getAiTag());
            activityText.setPrompt(fullPrompt); // 保存构建的prompt
        }

        // 调用AI模型生成文案
        String generatedText = aiModelService.generateText(fullPrompt, activityText.getModel());

        // 清理返回的JSON格式
        generatedText = TextUtils.cleanJsonResponse(generatedText);
        JSONObject jsonObject = JSON.parseObject(generatedText);
        if (StringUtils.isNotEmpty(activityText.getNameMode()) && activityText.getNameMode().equals("manual")) {

            if (null != jsonObject) {
                String content = jsonObject.getString("content");
                String topics = jsonObject.getString("topics");
                activityText.setTitle(topics);
                activityText.setResult(content);
            }
        } else {

            if (null != jsonObject) {
                String title = jsonObject.getString("title");
                String content = jsonObject.getString("content");
                String topics = jsonObject.getString("topics");
                activityText.setName(title);
                activityText.setTitle(topics);
                activityText.setResult(content);
            }
        }

        // 设置状态为正常生成
        activityText.setStatus("normal");

        // 保存生成的文案
        this.save(activityText);
        activityTextPreGenerateService.preGenerateTextAsync(activityText, null);

        return R.ok().put("activityText", activityText);
    }

    @Override
    public ActivityTextEntity findByActivityIdAndAdTypeAndAiTag(Long activityId, String adType, String aiTag) throws Exception {
        QueryWrapper<ActivityTextEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        wrapper.eq("ad_type", adType);
        if (StringUtils.isNotEmpty(aiTag)) {
            wrapper.eq("ai_tag", aiTag);
        } else {
            wrapper.and(w -> w.isNull("ai_tag").or().eq("ai_tag", ""));
        }
        wrapper.orderByDesc("create_on"); // 按创建时间倒序，获取最新的
        List<ActivityTextEntity> activityTextEntities = this.list(wrapper);

        if (CollectionUtils.isEmpty(activityTextEntities)) {
            // 直接生成文案
            ActivityTextEntity activityText = new ActivityTextEntity();
            activityText.setActivityId(activityId);
            activityText.setAdType(adType);
            activityText.setAiTag(aiTag);
            // 生成文案
            R result = this.generateText(activityText, null);
            if (!result.get("code").equals(200)) {
                throw new RuntimeException("生成文案失败");
            }
            return (ActivityTextEntity) result.get("activityText");
        } else {
            return activityTextEntities.get(0);
        }
    }

    @Override
    public ActivityTextEntity findByActivityIdAndAdTypeAndAiTagNotUse(Long activityId, String adType, String aiTag) throws Exception {
        QueryWrapper<ActivityTextEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("activity_id", activityId);
        wrapper.eq("ad_type", adType);
        if (StringUtils.isNotEmpty(aiTag)) {
            wrapper.eq("ai_tag", aiTag);
        } else {
            wrapper.and(w -> w.isNull("ai_tag").or().eq("ai_tag", ""));
        }
        wrapper.orderByDesc("create_on"); // 按创建时间倒序，获取最新的
        List<ActivityTextEntity> activityTextEntities = this.list(wrapper);

        if (CollectionUtils.isEmpty(activityTextEntities)) {
            // 预览模式不生成新文案，返回null
            return null;
        } else {
            // 预览模式不增加使用次数，直接返回
            return activityTextEntities.get(0);
        }
    }

}