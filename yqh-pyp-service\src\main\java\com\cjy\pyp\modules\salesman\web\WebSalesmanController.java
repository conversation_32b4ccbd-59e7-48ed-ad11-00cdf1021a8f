package com.cjy.pyp.modules.salesman.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityRechargePackageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargePackageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.vo.CreateActivityPackageOrderVo;
import com.cjy.pyp.modules.activity.vo.RechargeOrderVo;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionConfigEntity;
import com.cjy.pyp.modules.salesman.entity.SalesmanEntity;

import com.cjy.pyp.modules.salesman.entity.SalesmanQrcodeEntity;
import com.cjy.pyp.modules.salesman.entity.WxUserSalesmanBindingEntity;
import com.cjy.pyp.modules.salesman.service.OrderSalesmanAssociationService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionConfigService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;

import com.cjy.pyp.modules.salesman.service.SalesmanQrcodeService;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.salesman.service.WxUserSalesmanBindingService;
import com.cjy.pyp.modules.channel.entity.ChannelEntity;
import com.cjy.pyp.modules.channel.service.ChannelService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.modules.sys.entity.SysUserEntity;
import com.cjy.pyp.modules.wx.entity.WxUser;
import com.cjy.pyp.modules.wx.service.WxUserService;
import org.springframework.util.StringUtils;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务员前端控制器
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-25
 */
@RestController
@RequestMapping("web/salesman")
@Api(tags = {"业务员前端接口"})
public class WebSalesmanController extends AbstractController {
    
    @Autowired
    private SalesmanService salesmanService;
    
    @Autowired
    private SalesmanQrcodeService salesmanQrcodeService;
    

    
    @Autowired
    private ActivityRechargePackageService activityRechargePackageService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private WxUserService wxUserService;

    @Autowired
    private WxUserSalesmanBindingService wxUserSalesmanBindingService;

    @Autowired
    private SalesmanCommissionConfigService salesmanCommissionConfigService;

    @Autowired
    private OrderSalesmanAssociationService orderSalesmanAssociationService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private SalesmanCommissionRecordService salesmanCommissionRecordService;

    /**
     * 获取业务员统计数据（替代原来的 salesmanOrderService.getStatsBySalesmanId）
     */
    private Map<String, Object> getSalesmanStats(Long salesmanId, String appid) {
        Map<String, Object> stats = new HashMap<>();

        // 从充值记录中统计订单数据
        QueryWrapper<ActivityRechargeRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("salesman_id", salesmanId)
               .eq("appid", appid)
               .eq("status", 1); // 只统计已支付的订单

        List<ActivityRechargeRecordEntity> orders = activityRechargeRecordService.list(wrapper);

        int totalOrders = orders.size();
        int activityOrders = (int) orders.stream().filter(o -> o.getRechargeType() == 4).count(); // 创建活动套餐
        int rechargeOrders = (int) orders.stream().filter(o -> o.getRechargeType() == 1 || o.getRechargeType() == 2).count(); // 充值订单
        BigDecimal totalAmount = orders.stream()
                .map(o -> o.getPayAmount() != null ? o.getPayAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 从佣金记录中获取佣金统计
        Map<String, Object> commissionParams = new HashMap<>();
        commissionParams.put("salesmanId", salesmanId);
        commissionParams.put("appid", appid);
        Map<String, Object> commissionStats = salesmanCommissionRecordService.getCommissionStats(commissionParams);
        BigDecimal totalCommission = (BigDecimal) commissionStats.getOrDefault("totalAmount", BigDecimal.ZERO);

        stats.put("totalOrders", totalOrders);
        stats.put("activityOrders", activityOrders);
        stats.put("rechargeOrders", rechargeOrders);
        stats.put("totalAmount", totalAmount);
        stats.put("totalCommission", totalCommission);

        return stats;
    }

    // 通过id获取业务员
    @RequestMapping("/info/{id}")
    public R info(@PathVariable("id") Long id) {
        SalesmanEntity salesman = salesmanService.getById(id);
        return R.ok().put("result", salesman);
    }

    /**
     * 扫码页面 - 获取业务员信息和可选套餐
     */
    @RequestMapping("/scanByAuth")
    public R scan(@CookieValue String appid) {
        SysUserEntity user = getUser();
        // 获取业务员信息
        SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(),appid);
        if (salesman == null || !salesman.getStatus().equals(1)) {
            return R.error("业务员不存在或已禁用");
        }

        // 记录扫码次数
        // String qrcodeContent = buildQrcodeContent(salesmanId, activityId, appid);
        // SalesmanQrcodeEntity qrcode = salesmanQrcodeService.findByQrcodeContent(qrcodeContent);
        // if (qrcode != null) {
        //     salesmanQrcodeService.increaseScanCount(qrcode.getId());
        // }

        // 获取可选套餐
        // 获取充值套餐（类型1）
        // List<ActivityRechargePackageEntity> rechargePackages = activityRechargePackageService.getEnabledPackagesByAppidAndType(appid, 1);

        // 获取创建活动套餐（类型2）
        List<ActivityRechargePackageEntity> activityPackages = activityRechargePackageService.getEnabledPackagesByAppidAndType(appid, 2);

        return R.ok()
            .put("salesman", salesman)
            // .put("rechargePackages", rechargePackages)
            .put("activityPackages", activityPackages);
    }

    /**
     * 个人中心 - 检查业务员身份
     */
    @RequestMapping("/checkSalesmanStatus")
    @ApiOperation(value = "检查当前用户是否为业务员", notes = "用于个人中心页面判断是否显示业务员功能")
    public R checkSalesmanStatus(@CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.ok()
                    .put("isSalesman", false)
                    .put("message", "用户未登录或未绑定手机号");
            }

            // 获取业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.ok()
                    .put("isSalesman", false)
                    .put("message", "用户不是业务员或业务员已禁用");
            }

            // 获取业务员统计数据
            Map<String, Object> stats = getSalesmanStats(salesman.getId(), appid);

            // 获取子业务员数量
            int childrenCount = salesmanService.count(new QueryWrapper<SalesmanEntity>()
                .eq("parent_id", salesman.getId())
                .eq("status", 1)
                .eq("appid", appid));

            return R.ok()
                .put("isSalesman", true)
                .put("salesman", salesman)
                .put("stats", stats)
                .put("childrenCount", childrenCount)
                .put("message", "业务员身份验证成功");

        } catch (Exception e) {
            return R.error("检查业务员身份失败：" + e.getMessage());
        }
    }

    /**
     * 获取业务员详细统计数据
     */
    @RequestMapping("/getStats")
    @ApiOperation(value = "获取业务员详细统计数据", notes = "用于个人中心展示业务员的详细业务统计")
    public R getStats(@CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 获取业务员统计数据
            Map<String, Object> stats = getSalesmanStats(salesman.getId(), appid);

            // 获取子业务员数量
            int childrenCount = salesmanService.count(new QueryWrapper<SalesmanEntity>()
                .eq("parent_id", salesman.getId())
                .eq("status", 1)
                .eq("appid", appid));

            // 获取子业务员列表（只返回基本信息）
            List<SalesmanEntity> children = salesmanService.list(new QueryWrapper<SalesmanEntity>()
                .eq("parent_id", salesman.getId())
                .eq("status", 1)
                .eq("appid", appid)
                .select("id", "name", "mobile", "department", "position", "level", "create_on")
                .orderByDesc("create_on"));

            return R.ok()
                .put("salesman", salesman)
                .put("stats", stats)
                .put("childrenCount", childrenCount)
                .put("children", children);

        } catch (Exception e) {
            return R.error("获取统计数据失败：" + e.getMessage());
        }
    }

    /**
     * 获取子业务员列表
     */
    @RequestMapping("/getChildren")
    @ApiOperation(value = "获取子业务员列表", notes = "获取当前业务员的下级业务员列表")
    public R getChildren(@CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 获取子业务员列表（包含统计信息）
            List<SalesmanEntity> children = salesmanService.list(new QueryWrapper<SalesmanEntity>()
                .eq("parent_id", salesman.getId())
                .eq("status", 1)
                .eq("appid", appid)
                .orderByDesc("create_on"));

            // 为每个子业务员添加统计信息
            for (SalesmanEntity child : children) {
                Map<String, Object> childStats = getSalesmanStats(child.getId(), appid);
                child.setTotalOrders((Integer) childStats.get("totalOrders"));
                child.setTotalAmount((BigDecimal) childStats.get("totalAmount"));
                child.setTotalCommission((BigDecimal) childStats.get("totalCommission"));
                child.setActivityOrders((Integer) childStats.get("activityOrders"));
                child.setRechargeOrders((Integer) childStats.get("rechargeOrders"));

                // 获取子业务员的下级数量
                int grandChildrenCount = salesmanService.count(new QueryWrapper<SalesmanEntity>()
                    .eq("parent_id", child.getId())
                    .eq("status", 1)
                    .eq("appid", appid));
                child.setChildrenCount(grandChildrenCount);
            }

            return R.ok()
                .put("children", children)
                .put("total", children.size());

        } catch (Exception e) {
            return R.error("获取子业务员列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取子业务员详细统计
     */
    @RequestMapping("/getChildStats")
    @ApiOperation(value = "获取子业务员详细统计", notes = "获取指定子业务员的详细统计信息")
    public R getChildStats(@RequestParam("childId") Long childId, @CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取当前业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 获取子业务员信息并验证权限
            SalesmanEntity child = salesmanService.getById(childId);
            if (child == null || !child.getParentId().equals(salesman.getId()) || !child.getAppid().equals(appid)) {
                return R.error("无权查看该业务员信息");
            }

            // 获取子业务员统计数据
            Map<String, Object> stats = getSalesmanStats(childId, appid);

            // 获取子业务员的下级数量
            int grandChildrenCount = salesmanService.count(new QueryWrapper<SalesmanEntity>()
                .eq("parent_id", childId)
                .eq("status", 1)
                .eq("appid", appid));

            return R.ok()
                .put("child", child)
                .put("stats", stats)
                .put("grandChildrenCount", grandChildrenCount);

        } catch (Exception e) {
            return R.error("获取子业务员统计失败：" + e.getMessage());
        }
    }

    /**
     * 生成业务员邀请链接
     */
    @RequestMapping("/generateInviteLink")
    @ApiOperation(value = "生成业务员邀请链接", notes = "生成用于邀请他人成为下级业务员的链接")
    public R generateInviteLink(@CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 生成邀请链接（这里可以根据实际需求调整链接格式）
            String baseUrl = "https://yqihua.com/p_front/#/salesman/register";
            String inviteLink = baseUrl + "?inviterId=" + salesman.getId() + "&appid=" + appid;

            // 生成邀请二维码内容
            String qrcodeContent = inviteLink;

            return R.ok()
                .put("inviteLink", inviteLink)
                .put("qrcodeContent", qrcodeContent)
                .put("inviterName", salesman.getName())
                .put("inviterMobile", salesman.getMobile());

        } catch (Exception e) {
            return R.error("生成邀请链接失败：" + e.getMessage());
        }
    }

    /**
     * 处理邀请注册
     */
    @RequestMapping("/processInvite")
    @ApiOperation(value = "处理邀请注册", notes = "处理通过邀请链接注册的业务员")
    public R processInvite(@RequestParam("inviterId") Long inviterId,
                          @RequestParam("name") String name,
                          @RequestParam("mobile") String mobile,
                          @RequestParam(value = "email", required = false) String email,
                          @RequestParam(value = "department", required = false) String department,
                          @RequestParam(value = "position", required = false) String position,
                          @CookieValue String appid) {
        try {
            // 验证邀请人
            SalesmanEntity inviter = salesmanService.getById(inviterId);
            if (inviter == null || !inviter.getStatus().equals(1) || !inviter.getAppid().equals(appid)) {
                return R.error("邀请人不存在或已禁用");
            }

            // 检查手机号是否已存在
            if (salesmanService.existsByMobile(mobile, appid, null)) {
                return R.error("该手机号已注册为业务员");
            }

            // 生成业务员编号
            String code = "YWY" + System.currentTimeMillis();

            // 创建新业务员
            SalesmanEntity newSalesman = new SalesmanEntity();
            newSalesman.setName(name);
            newSalesman.setMobile(mobile);
            newSalesman.setEmail(email);
            newSalesman.setCode(code);
            newSalesman.setDepartment(department);
            newSalesman.setPosition(position);
            newSalesman.setStatus(1);
            newSalesman.setParentId(inviterId);
            newSalesman.setChannelId(inviter.getChannelId());
            newSalesman.setLevel(inviter.getLevel() + 1);
            newSalesman.setAppid(appid);

            salesmanService.save(newSalesman);

            // 更新邀请人的层级信息
            salesmanService.updateLevel(inviterId);

            return R.ok()
                .put("message", "邀请注册成功")
                .put("salesman", newSalesman);

        } catch (Exception e) {
            return R.error("处理邀请注册失败：" + e.getMessage());
        }
    }

    /**
     * 处理渠道邀请注册
     */
    @RequestMapping("/processChannelInvite")
    @ApiOperation(value = "处理渠道邀请注册", notes = "处理通过渠道邀请链接注册的业务员")
    public R processChannelInvite(@RequestParam("channelId") Long channelId,
                                 @RequestParam("name") String name,
                                 @RequestParam("mobile") String mobile,
                                 @RequestParam(value = "email", required = false) String email,
                                 @RequestParam(value = "department", required = false) String department,
                                 @RequestParam(value = "position", required = false) String position,
                                 @CookieValue String appid) {
        try {
            // 验证渠道
            ChannelEntity channel = channelService.getById(channelId);
            if (channel == null || !channel.getStatus().equals(1) || !channel.getAppid().equals(appid)) {
                return R.error("渠道不存在或已禁用");
            }

            // 检查手机号是否已存在
            if (salesmanService.existsByMobile(mobile, appid, null)) {
                return R.error("该手机号已注册为业务员");
            }

            // 生成业务员编号
            String code = "YWY" + System.currentTimeMillis();

            // 创建新业务员
            SalesmanEntity newSalesman = new SalesmanEntity();
            newSalesman.setName(name);
            newSalesman.setMobile(mobile);
            newSalesman.setEmail(email);
            newSalesman.setCode(code);
            newSalesman.setDepartment(department);
            newSalesman.setPosition(position);
            newSalesman.setStatus(1);
            newSalesman.setParentId(null); // 渠道邀请的业务员没有上级业务员
            newSalesman.setChannelId(channelId);
            newSalesman.setLevel(1); // 渠道邀请的业务员为一级业务员
            newSalesman.setAppid(appid);

            salesmanService.save(newSalesman);

            return R.ok()
                .put("message", "渠道邀请注册成功")
                .put("salesman", newSalesman);

        } catch (Exception e) {
            return R.error("处理渠道邀请注册失败：" + e.getMessage());
        }
    }

    /**
     * 获取业务员的客户绑定记录
     */
    @RequestMapping("/getCustomerBindings")
    @ApiOperation(value = "获取客户绑定记录", notes = "获取当前业务员的客户绑定记录列表")
    public R getCustomerBindings(@RequestParam(defaultValue = "1") Integer page,
                                @RequestParam(defaultValue = "10") Integer limit,
                                @RequestParam(required = false) String keyword,
                                @RequestParam(required = false) Integer status,
                                @RequestParam(required = false) Integer bindingType,
                                @RequestParam(required = false) Integer sortType,
                                @CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("salesmanId", salesman.getId());
            params.put("appid", appid);
            params.put("page", page);
            params.put("limit", limit);

            if (keyword != null && !keyword.trim().isEmpty()) {
                params.put("wxUserName", keyword.trim());
            }
            if (status != null && status > 0) {
                params.put("status", status);
            }
            if (bindingType != null && bindingType > 0) {
                params.put("bindingType", bindingType);
            }
            if (sortType != null) {
                params.put("sortType", sortType);
            }

            // 获取绑定记录
            List<WxUserSalesmanBindingEntity> bindings = wxUserSalesmanBindingService.queryPage(params);

            return R.ok().put("result", bindings);

        } catch (Exception e) {
            return R.error("获取客户绑定记录失败：" + e.getMessage());
        }
    }

    /**
     * 获取业务员的佣金规则
     */
    @RequestMapping("/getCommissionRules")
    @ApiOperation(value = "获取佣金规则", notes = "获取当前业务员的佣金规则配置")
    public R getCommissionRules(@CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取业务员信息
            SalesmanEntity salesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (salesman == null || !salesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 获取佣金规则
            List<SalesmanCommissionConfigEntity> rules = salesmanCommissionConfigService.getEffectiveConfigsBySalesman(salesman.getId(),appid);

            return R.ok().put("result", rules);

        } catch (Exception e) {
            return R.error("获取佣金规则失败：" + e.getMessage());
        }
    }

    /**
     * 获取客户订单列表
     */
    @RequestMapping("/getCustomerOrders")
    @ApiOperation(value = "获取客户订单列表", notes = "业务员查看指定客户的订单列表")
    public R getCustomerOrders(@RequestParam("wxUserId") Long wxUserId,
                              @RequestParam(value = "page", defaultValue = "1") Integer page,
                              @RequestParam(value = "limit", defaultValue = "10") Integer limit,
                              @RequestParam(value = "status", required = false) String status,
                              @CookieValue String appid) {
        try {
            // 获取当前登录用户
            Long userId = getUserId();

            // 根据用户手机号查找业务员
            WxUser wxUser = wxUserService.getById(userId);
            if (wxUser == null || !StringUtils.hasText(wxUser.getMobile())) {
                return R.error("用户信息不完整");
            }

            SalesmanEntity salesman = salesmanService.findByMobile(wxUser.getMobile(), appid);
            if (salesman == null) {
                return R.error("您不是业务员，无法查看客户订单");
            }

            // 验证客户是否属于该业务员
            WxUserSalesmanBindingEntity binding = wxUserSalesmanBindingService.getActiveBindingByWxUser(wxUserId, appid);
            if (binding == null || !binding.getSalesmanId().equals(salesman.getId())) {
                return R.error("该客户不属于您的客户");
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", appid);
            params.put("wxUserId", wxUserId);
            params.put("salesmanId", salesman.getId());
            params.put("page", page);
            params.put("limit", limit);

            if (StringUtils.hasText(status) && !status.equals("")) {
                params.put("status", Integer.parseInt(status));
            }

            // 查询订单列表
            List<ActivityRechargeRecordEntity> orders = orderSalesmanAssociationService.queryOrderList(params);

            return R.ok().put("result", orders);

        } catch (Exception e) {
            logger.error("获取客户订单失败", e);
            return R.error("获取客户订单失败：" + e.getMessage());
        }
    }

    /**
     * 扫码页面 - 获取业务员信息和可选套餐
     */
    @RequestMapping("/scan")
    @ApiOperation(value = "扫码获取业务员信息和套餐", notes = "")
    public R scan(@RequestParam("salesmanId") Long salesmanId,
                  @RequestParam(value = "activityId", required = false) Long activityId,
                  @CookieValue String appid) {
        
        // 获取业务员信息
        SalesmanEntity salesman = salesmanService.getById(salesmanId);
        if (salesman == null || !salesman.getStatus().equals(1)) {
            return R.error("业务员不存在或已禁用");
        }
        
        // 记录扫码次数
        String qrcodeContent = buildQrcodeContent(salesmanId, activityId, appid);
        SalesmanQrcodeEntity qrcode = salesmanQrcodeService.findByQrcodeContent(qrcodeContent);
        if (qrcode != null) {
            salesmanQrcodeService.increaseScanCount(qrcode.getId());
        }
        
        // 获取可选套餐
        // 获取充值套餐（类型1）
        // List<ActivityRechargePackageEntity> rechargePackages = activityRechargePackageService.getEnabledPackagesByAppidAndType(appid, 1);

        // 获取创建活动套餐（类型2）
        List<ActivityRechargePackageEntity> activityPackages = activityRechargePackageService.getEnabledPackagesByAppidAndType(appid, 2);

        return R.ok()
            .put("salesman", salesman)
            // .put("rechargePackages", rechargePackages)
            .put("activityPackages", activityPackages);
    }

    /**
     * 创建订单
     */
    @RequestMapping("/createOrder")
    // @SysLog("通过业务员二维码创建订单")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "创建订单", notes = "")
    public R createOrder(@RequestParam("salesmanId") Long salesmanId,
                        @RequestParam("packageId") Long packageId,
                        @RequestParam(value = "activityName", required = false) String activityName,
                        @CookieValue String appid) {
        
        // 验证业务员
        SalesmanEntity salesman = salesmanService.getById(salesmanId);
        if (salesman == null || !salesman.getStatus().equals(1)) {
            return R.error("业务员不存在或已禁用");
        }
        
        // 获取当前用户
        WxUser wxUser = wxUserService.getById(getUserId());
        if (wxUser == null) {
            return R.error(401,"用户未登录");
        }
        
        // try {
            // 获取套餐信息
            ActivityRechargePackageEntity packageEntity = activityRechargePackageService.getById(packageId);
            if (packageEntity == null || !packageEntity.getStatus().equals(1)) {
                return R.error("套餐不存在或已禁用");
            }

            Long orderId;
            BigDecimal orderAmount = packageEntity.getPrice();
            Integer orderType;

            if (packageEntity.getPackageType() == 1) {
                // 充值套餐订单
                orderId = createRechargeOrder(packageEntity, wxUser, appid);
                orderType = 2; // 充值订单

            } else if (packageEntity.getPackageType() == 2) {
                // 创建活动套餐订单
                if (activityName == null || activityName.trim().isEmpty()) {
                    return R.error("活动名称不能为空");
                }
                orderId = createActivityPackageOrder(packageEntity, wxUser, activityName, appid);
                orderType = 3; // 创建活动订单

            } else {
                return R.error("不支持的套餐类型");
            }

            // 确保订单关联到业务员（更新充值记录的 salesman_id 字段）
            ActivityRechargeRecordEntity rechargeRecord = activityRechargeRecordService.getById(orderId);
            if (rechargeRecord != null && rechargeRecord.getSalesmanId() == null) {
                rechargeRecord.setSalesmanId(salesmanId);
                activityRechargeRecordService.updateById(rechargeRecord);
            }

            // 更新二维码统计
            String qrcodeContent = buildQrcodeContent(salesmanId, null, appid);
            SalesmanQrcodeEntity qrcode = salesmanQrcodeService.findByQrcodeContent(qrcodeContent);
            if (qrcode != null) {
                salesmanQrcodeService.increaseOrderStats(qrcode.getId(), orderAmount);
            }

            return R.ok().put("orderId", orderId).put("orderAmount", orderAmount).put("orderType", orderType);

        // } catch (Exception e) {
        //     return R.error("创建订单失败：" + e.getMessage());
        // }
    }

    /**
     * 创建充值订单
     */
    private Long createRechargeOrder(ActivityRechargePackageEntity packageEntity, WxUser wxUser, String appid) {
        // 创建充值订单VO
        RechargeOrderVo rechargeOrderVo = new RechargeOrderVo();
        rechargeOrderVo.setPackageId(packageEntity.getId());
        rechargeOrderVo.setActivityId(1L); // 使用默认活动ID，或者从套餐中获取
        rechargeOrderVo.setRechargeType(1); // 套餐充值
        rechargeOrderVo.setAppid(appid);
        rechargeOrderVo.setRepeatToken("salesman_" + System.currentTimeMillis()); // 防重复令牌

        // 调用现有的充值订单创建服务
        R result = activityRechargeRecordService.createRechargeOrder(rechargeOrderVo, wxUser.getId());
        if (result.get("code").equals(200)) {
            Long record = (Long) result.get("orderId");
            return record;
        } else {
            throw new RuntimeException("创建充值订单失败：" + result.get("msg"));
        }
    }

    /**
     * 创建活动套餐订单
     */
    private Long createActivityPackageOrder(ActivityRechargePackageEntity packageEntity, WxUser wxUser, String activityName, String appid) {
        // 创建活动套餐订单VO
        CreateActivityPackageOrderVo orderVo = new CreateActivityPackageOrderVo();
        orderVo.setPackageId(packageEntity.getId());
        orderVo.setActivityName(activityName);
        orderVo.setAppid(appid);
        orderVo.setRepeatToken("salesman_" + System.currentTimeMillis()); // 防重复令牌

        // 调用现有的活动套餐订单创建服务
        R result = activityRechargeRecordService.createActivityPackageOrder(orderVo, wxUser.getId());
        if (result.get("code").equals(200)) {
            Long record = (Long) result.get("orderId");
            return record;
        } else {
            throw new RuntimeException("创建活动套餐订单失败：" + result.get("msg"));
        }
    }

    /**
     * 构建二维码内容
     */
    private String buildQrcodeContent(Long salesmanId, Long activityId, String appid) {
        StringBuilder content = new StringBuilder();
        content.append("https://yqihua.com/p_front/#/salesman/scan");
        content.append("?salesmanId=").append(salesmanId);
        
        if (activityId != null) {
            content.append("&activityId=").append(activityId);
        }
        
        return content.toString();
    }

    /**
     * 获取充值订单详情
     */
    @RequestMapping("/getRechargeOrderInfo")
    @ApiOperation(value = "获取充值订单详情", notes = "")
    public R getRechargeOrderInfo(@RequestParam("orderId") Long orderId) {
        ActivityRechargeRecordEntity record = activityRechargeRecordService.getById(orderId);
        if (record == null) {
            return R.error("订单不存在");
        }

        // 获取套餐信息
        ActivityRechargePackageEntity packageEntity = activityRechargePackageService.getById(record.getPackageId());

        return R.ok()
            .put("orderInfo", record)
            .put("packageInfo", packageEntity);
    }

    /**
     * 获取业务员订单列表
     */
    @RequestMapping("/orders")
    @ApiOperation(value = "获取业务员订单列表", notes = "获取业务员的订单列表，支持筛选")
    public R getOrders(@RequestParam Map<String, Object> params, @CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取当前业务员信息
            SalesmanEntity currentSalesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (currentSalesman == null || !currentSalesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 设置查询参数
            params.put("appid", appid);
            params.put("currentSalesmanId", currentSalesman.getId());

            // 获取筛选类型
            String type = (String) params.get("type");
            if ("my".equals(type)) {
                params.put("type", "my");
            } else if ("team".equals(type)) {
                params.put("type", "team");
            } else {
                // "all" 查询所有相关订单（自己+团队）
                params.put("salesmanId", currentSalesman.getId());
            }

            // 时间筛选
            String timeRange = (String) params.get("timeRange");
            if (timeRange != null && !timeRange.equals("all")) {
                params.put("timeRange", timeRange);
            }

            // 状态筛选
            String status = (String) params.get("status");
            if (status != null && !status.equals("all")) {
                params.put("status", status);
            }

            // 分页查询订单（使用新的订单关联服务）
            List<ActivityRechargeRecordEntity> orders;
            if ("team".equals(type)) {
                // 查询团队订单 - 需要包含下级业务员的订单
                // 先获取团队业务员ID列表
                List<SalesmanEntity> teamSalesmen = salesmanService.list(
                    new QueryWrapper<SalesmanEntity>()
                        .eq("parent_id", currentSalesman.getId())
                        .eq("status", 1)
                );
                List<Long> teamSalesmanIds = teamSalesmen.stream()
                    .map(SalesmanEntity::getId)
                    .collect(Collectors.toList());
                teamSalesmanIds.add(currentSalesman.getId()); // 包含自己

                params.put("salesmanIds", teamSalesmanIds);
                orders = orderSalesmanAssociationService.queryOrderList(params);
            } else {
                // 查询个人订单
                params.put("salesmanId", currentSalesman.getId());
                orders = orderSalesmanAssociationService.queryOrderList(params);
            }

            Map<String, Object> pageResult = new HashMap<>();
            pageResult.put("list", orders);
            return R.ok().put("page", pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("获取订单列表失败");
        }
    }

    /**
     * 获取业务员订单统计
     */
    @RequestMapping("/orderStats")
    @ApiOperation(value = "获取业务员订单统计", notes = "获取业务员的订单统计信息")
    public R getOrderStats(@CookieValue String appid) {
        try {
            SysUserEntity user = getUser();
            if (user == null || user.getMobile() == null) {
                return R.error("用户未登录或未绑定手机号");
            }

            // 获取当前业务员信息
            SalesmanEntity currentSalesman = salesmanService.findByMobile(user.getMobile(), appid);
            if (currentSalesman == null || !currentSalesman.getStatus().equals(1)) {
                return R.error("业务员不存在或已禁用");
            }

            // 获取订单统计
            Map<String, Object> stats = getSalesmanStats(currentSalesman.getId(), appid);

            // 获取今日订单数量
            LocalDate today = LocalDate.now();
            int todayOrders = activityRechargeRecordService.count(
                new QueryWrapper<ActivityRechargeRecordEntity>()
                    .eq("salesman_id", currentSalesman.getId())
                    .eq("status", 1) // 已支付
                    .ge("create_on", today.atStartOfDay())
                    .lt("create_on", today.plusDays(1).atStartOfDay())
            );

            // 获取本月业绩
            LocalDate monthStart = today.withDayOfMonth(1);
            BigDecimal monthAmount = activityRechargeRecordService.list(
                new QueryWrapper<ActivityRechargeRecordEntity>()
                    .eq("salesman_id", currentSalesman.getId())
                    .eq("status", 1) // 已支付
                    .ge("create_on", monthStart.atStartOfDay())
                    .lt("create_on", monthStart.plusMonths(1).atStartOfDay())
            ).stream()
            .map(r -> r.getPayAmount() != null ? r.getPayAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, Object> result = new HashMap<>();
            result.put("totalOrders", stats.get("totalOrders") != null ? stats.get("totalOrders") : 0);
            result.put("totalAmount", stats.get("totalAmount") != null ? stats.get("totalAmount") : BigDecimal.ZERO);
            result.put("todayOrders", todayOrders);
            result.put("monthAmount", monthAmount);

            return R.ok().put("stats", result);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("获取订单统计失败");
        }
    }


}
