# 点评功能 AI 标签增强文档

## 功能概述

为点评功能的文案查询添加 AI 标签支持，实现更精准的文案匹配。当前端没有传入 AI 标签时，系统会自动从活动配置中获取默认的 AI 标签。

## 主要改进

### 1. 文案查询支持 AI 标签

**原实现**：
- 只根据 `activityId` 和 `adType` 查询文案
- 无法根据用户特征进行精准匹配

**新实现**：
- 支持根据 `activityId`、`adType` 和 `aiTag` 查询文案
- 如果前端没有传入 AI 标签，自动从活动配置中获取
- 实现更精准的文案匹配

### 2. 预览 API 增强

所有预览 API 都新增了 `aiTag` 参数支持：

- `GET /web/activity/review/douyin/preview?activityId={id}&aiTag={tag}`
- `GET /web/activity/review/dianping/preview?activityId={id}&aiTag={tag}`
- `GET /web/activity/review/meituan/preview?activityId={id}&aiTag={tag}`
- `GET /web/activity/review/ctrip/preview?activityId={id}&type={type}&aiTag={tag}`

### 3. 智能标签获取

当前端没有传入 `aiTag` 参数时，系统会：
1. 从 `tb_activity` 表中查询活动的 `ai_tag` 字段
2. 使用活动的默认 AI 标签进行文案查询
3. 确保文案与活动配置的受众群体匹配

## 技术实现

### 后端实现

#### 1. 新增服务方法

**ActivityTextService 接口**：
```java
/**
 * 根据活动ID、广告类型和AI标签查询文案（不增加使用次数）
 */
ActivityTextEntity findByActivityIdAndAdTypeAndAiTagNotUse(Long activityId, String adType, String aiTag) throws Exception;
```

**ActivityTextServiceImpl 实现**：
```java
@Override
public ActivityTextEntity findByActivityIdAndAdTypeAndAiTagNotUse(Long activityId, String adType, String aiTag) throws Exception {
    QueryWrapper<ActivityTextEntity> wrapper = new QueryWrapper<>();
    wrapper.eq("activity_id", activityId);
    wrapper.eq("ad_type", adType);
    if (StringUtils.isNotEmpty(aiTag)) {
        wrapper.eq("ai_tag", aiTag);
    } else {
        wrapper.and(w -> w.isNull("ai_tag").or().eq("ai_tag", ""));
    }
    wrapper.orderByDesc("create_on");
    List<ActivityTextEntity> activityTextEntities = this.list(wrapper);

    if (CollectionUtils.isEmpty(activityTextEntities)) {
        // 预览模式不生成新文案，返回null
        return null;
    } else {
        // 预览模式不增加使用次数，直接返回
        return activityTextEntities.get(0);
    }
}
```

#### 2. 控制器方法增强

**预览方法示例**：
```java
@GetMapping("/douyin/preview")
public R previewDouyinReview(@RequestParam("activityId") Long activityId,
                            @RequestParam(value = "aiTag", required = false) String aiTag) {
    try {
        return getReviewContentPreview(activityId, "douyin_review", "抖音点评", aiTag);
    } catch (Exception e) {
        return R.error("获取抖音点评内容失败: " + e.getMessage());
    }
}
```

**核心预览方法**：
```java
private R getReviewContentPreview(Long activityId, String adType, String platformName, String aiTag) throws Exception {
    Map<String, Object> result = new HashMap<>();

    // 如果没有传入AI标签，从活动中获取
    if (StringUtils.isBlank(aiTag)) {
        ActivityEntity activity = activityService.getById(activityId);
        if (activity != null) {
            aiTag = activity.getAiTag();
        }
    }

    // 查询对应平台的文案 - 使用支持AI标签的方法，但不增加使用次数
    ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeAndAiTagNotUse(activityId, adType, aiTag);
    
    // ... 其他逻辑
}
```

### 数据库查询逻辑

#### 1. 有 AI 标签时
```sql
SELECT * FROM activity_text 
WHERE activity_id = ? 
  AND ad_type = ? 
  AND ai_tag = ?
ORDER BY create_on DESC
LIMIT 1
```

#### 2. 无 AI 标签时（通用文案）
```sql
SELECT * FROM activity_text 
WHERE activity_id = ? 
  AND ad_type = ? 
  AND (ai_tag IS NULL OR ai_tag = '')
ORDER BY create_on DESC
LIMIT 1
```

## 使用场景

### 场景1：前端传入 AI 标签
```javascript
// 前端调用
this.$fly.get('/pyp/web/activity/review/douyin/preview', {
  activityId: 123456,
  aiTag: '年轻女性'
});
```

**处理流程**：
1. 使用传入的 `aiTag = '年轻女性'`
2. 查询匹配该标签的文案
3. 返回针对年轻女性的文案内容

### 场景2：前端未传入 AI 标签
```javascript
// 前端调用
this.$fly.get('/pyp/web/activity/review/douyin/preview', {
  activityId: 123456
  // 没有传入 aiTag
});
```

**处理流程**：
1. 从 `tb_activity` 表查询活动的 `ai_tag` 字段
2. 假设活动配置的 AI 标签为 `'商务人士,白领'`
3. 使用活动的默认标签查询文案
4. 返回针对商务人士的文案内容

### 场景3：活动也没有配置 AI 标签
```javascript
// 前端调用（同场景2）
```

**处理流程**：
1. 前端没有传入 `aiTag`
2. 活动的 `ai_tag` 字段也为空
3. 查询通用文案（`ai_tag` 为空的文案）
4. 返回通用文案内容

## API 参数说明

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| activityId | Long | 是 | 活动ID |
| aiTag | String | 否 | AI标签，不传入时使用活动默认标签 |
| type | String | 否 | 携程专用，区分点评和笔记 |

### 响应数据

```json
{
  "code": 200,
  "result": {
    "platform": "抖音点评",
    "platformCode": "douyin_review",
    "title": "文案标题",
    "content": "根据AI标签生成的精准文案内容",
    "textId": 789,
    "images": [...],
    "hasImages": true
  }
}
```

## 优势对比

| 特性 | 原实现 | 新实现 |
|------|--------|--------|
| 文案匹配 | 通用匹配 | 精准匹配 |
| 受众定向 | 不支持 | 支持 |
| 标签来源 | 无 | 前端传入 + 活动配置 |
| 兼容性 | - | 完全向后兼容 |
| 智能回退 | 无 | 自动使用活动默认标签 |

## 兼容性保证

### 向后兼容
- 所有现有的 API 调用都能正常工作
- 不传入 `aiTag` 参数时，行为与之前一致
- 前端无需强制升级

### 渐进式升级
- 前端可以逐步添加 AI 标签支持
- 支持部分页面使用新功能，部分页面保持原样
- 降低升级风险

## 测试验证

### 功能测试
- [ ] 传入 AI 标签时返回匹配文案
- [ ] 不传入 AI 标签时使用活动默认标签
- [ ] 活动无标签时返回通用文案
- [ ] 所有平台的预览 API 都支持 AI 标签

### 兼容性测试
- [ ] 原有 API 调用方式仍然正常
- [ ] 新旧版本前端都能正常工作
- [ ] 数据库查询性能无明显下降

### 边界测试
- [ ] AI 标签为空字符串的处理
- [ ] 活动不存在时的处理
- [ ] 无匹配文案时的处理

## 后续优化

1. **缓存优化**: 缓存活动的 AI 标签，减少数据库查询
2. **性能优化**: 为 AI 标签相关查询添加数据库索引
3. **监控告警**: 添加文案匹配成功率的监控
4. **智能推荐**: 根据用户行为智能推荐最佳 AI 标签
