package com.cjy.pyp.modules.activity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;

import java.util.Map;
import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
public interface ActivityTextService extends IService<ActivityTextEntity> {

    PageUtils queryPage(Map<String, Object> params);

    List<ActivityTextEntity> findByIds(List<Long> ids);

    /**
     * 根据活动ID查询未使用的文案（useCount=0）
     * @param activityId 活动ID
     * @return 文案实体
     */
    ActivityTextEntity findUnusedByActivityId(Long activityId);

    /**
     * 增加文案使用次数
     * @param textId 文案ID
     */
    void incrementUseCount(Long textId);

    /**
     * 根据活动ID和广告类型查询文案
     * @param activityId 活动ID
     * @param adType 广告类型
     * @return 文案实体
     */
    ActivityTextEntity findByActivityIdAndAdType(Long activityId, String adType) throws Exception;
    ActivityTextEntity findByActivityIdAndAdTypeNotUse(Long activityId, String adType) throws Exception;

    /**
     * 根据活动ID、广告类型和AI标签查询文案
     * @param activityId 活动ID
     * @param adType 广告类型
     * @param aiTag AI标签
     * @return 文案实体
     */
    ActivityTextEntity findByActivityIdAndAdTypeAndAiTag(Long activityId, String adType, String aiTag) throws Exception;

    /**
     * 根据活动ID、广告类型和AI标签查询文案（不增加使用次数）
     * @param activityId 活动ID
     * @param adType 广告类型
     * @param aiTag AI标签
     * @return 文案实体
     */
    ActivityTextEntity findByActivityIdAndAdTypeAndAiTagNotUse(Long activityId, String adType, String aiTag) throws Exception;

    /**
     * 生成AI文案
     * @param activityText 文案实体
     * @param userId 用户ID
     * @return 生成结果
     * @throws Exception 生成失败时抛出异常
     */
    R generateText(ActivityTextEntity activityText, Long userId) throws Exception;
}

