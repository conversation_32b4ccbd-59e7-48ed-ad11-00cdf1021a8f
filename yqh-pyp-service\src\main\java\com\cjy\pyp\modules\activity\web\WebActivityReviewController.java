package com.cjy.pyp.modules.activity.web;

import com.cjy.pyp.common.utils.R;
import com.cjy.pyp.modules.activity.entity.ActivityEntity;
import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityTextEntity;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityService;
import com.cjy.pyp.modules.activity.service.ActivityTextService;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动点评Web接口
 */
@RestController
@RequestMapping("web/activity/review")
public class WebActivityReviewController extends AbstractController {

    @Autowired
    private ActivityTextService activityTextService;

    @Autowired
    private ActivityImageService activityImageService;

    @Autowired
    private ActivityRechargeRecordService activityRechargeRecordService;

    @Autowired
    private ActivityService activityService;

    /**
     * 预览抖音点评内容（不消耗次数）
     */
    @GetMapping("/douyin/preview")
    public R previewDouyinReview(@RequestParam("activityId") Long activityId,
                                @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            return getReviewContentPreview(activityId, "douyin_review", "抖音点评", aiTag);
        } catch (Exception e) {
            return R.error("获取抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 确认抖音点评跳转（消耗次数）
     */
    @RequestMapping("/douyin/confirm")
    public R confirmDouyinReview(@RequestParam("activityId") Long activityId,
                                @RequestParam(value = "textId", required = false) Long textId,
                                @RequestParam(value = "imageIds", required = false) String imageIds) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "抖音点评"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            // 消耗指定的文案和图片
            return consumeSpecificContent(activityId, "douyin_review", "抖音点评", textId, imageIds);
        } catch (Exception e) {
            return R.error("确认抖音点评失败: " + e.getMessage());
        }
    }

    /**
     * 获取抖音点评内容（兼容旧版本）
     */
    @GetMapping("/douyin")
    public R getDouyinReview(@RequestParam("activityId") Long activityId) {
        try {


        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "抖音点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "douyin_review", "抖音点评");
        } catch (Exception e) {
            return R.error("获取抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 预览大众点评内容（不消耗次数）
     */
    @GetMapping("/dianping/preview")
    public R previewDianpingReview(@RequestParam("activityId") Long activityId,
                                  @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            return getReviewContentPreview(activityId, "dianping", "大众点评", aiTag);
        } catch (Exception e) {
            return R.error("获取大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 确认大众点评跳转（消耗次数）
     */
    @RequestMapping("/dianping/confirm")
    public R confirmDianpingReview(@RequestParam("activityId") Long activityId,
                                  @RequestParam(value = "textId", required = false) Long textId,
                                  @RequestParam(value = "imageIds", required = false) String imageIds) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "大众点评"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            // 消耗指定的文案和图片
            return consumeSpecificContent(activityId, "dianping", "大众点评", textId, imageIds);
        } catch (Exception e) {
            return R.error("确认大众点评失败: " + e.getMessage());
        }
    }

    /**
     * 获取大众点评内容（兼容旧版本）
     */
    @GetMapping("/dianping")
    public R getDianpingReview(@RequestParam("activityId") Long activityId) {
        try {

        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "大众点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "dianping", "大众点评");
        } catch (Exception e) {
            return R.error("获取大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 预览美团点评内容（不消耗次数）
     */
    @GetMapping("/meituan/preview")
    public R previewMeituanReview(@RequestParam("activityId") Long activityId,
                                 @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            return getReviewContentPreview(activityId, "meituan", "美团点评", aiTag);
        } catch (Exception e) {
            return R.error("获取美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 确认美团点评跳转（消耗次数）
     */
    @RequestMapping("/meituan/confirm")
    public R confirmMeituanReview(@RequestParam("activityId") Long activityId,
                                 @RequestParam(value = "textId", required = false) Long textId,
                                 @RequestParam(value = "imageIds", required = false) String imageIds) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "美团点评"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            // 消耗指定的文案和图片
            return consumeSpecificContent(activityId, "meituan", "美团点评", textId, imageIds);
        } catch (Exception e) {
            return R.error("确认美团点评失败: " + e.getMessage());
        }
    }

    /**
     * 获取美团点评内容（兼容旧版本）
     */
    @GetMapping("/meituan")
    public R getMeituanReview(@RequestParam("activityId") Long activityId) {
        try {

        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                  "美团点评"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }
            return getReviewContent(activityId, "meituan", "美团点评");
        } catch (Exception e) {
            return R.error("获取美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 预览携程点评内容（不消耗次数）
     */
    @GetMapping("/ctrip/preview")
    public R previewCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "aiTag", required = false) String aiTag) throws Exception {

        // 根据type参数决定使用哪种广告类型
        String adType;
        String platformName;

        if ("notes".equals(type)) {
            adType = "ctrip_notes";
            platformName = "携程笔记";
        } else if ("review".equals(type)) {
            adType = "ctrip_review";
            platformName = "携程点评";
        } else {
            // 默认使用携程点评（兼容旧版）
            adType = "ctrip";
            platformName = "携程点评";
        }

        return getReviewContentPreview(activityId, adType, platformName, aiTag);
    }

    /**
     * 确认携程点评跳转（消耗次数）
     */
    @RequestMapping("/ctrip/confirm")
    public R confirmCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "textId", required = false) Long textId,
            @RequestParam(value = "imageIds", required = false) String imageIds) throws Exception {

        // 根据type参数决定使用哪种广告类型
        String adType;
        String platformName;

        if ("notes".equals(type)) {
            adType = "ctrip_notes";
            platformName = "携程笔记";
        } else if ("review".equals(type)) {
            adType = "ctrip_review";
            platformName = "携程点评";
        } else {
            // 默认使用携程点评（兼容旧版）
            adType = "ctrip";
            platformName = "携程点评";
        }

        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                 platformName + "转发"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }

        // 消耗指定的文案和图片
        return consumeSpecificContent(activityId, adType, platformName, textId, imageIds);
    }

    /**
     * 获取携程点评内容（兼容旧版）
     */
    @GetMapping("/ctrip")
    public R getCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type) throws Exception {


        // 根据type参数决定使用哪种广告类型
        String adType;
        String platformName;

        if ("notes".equals(type)) {
            adType = "ctrip_notes";
            platformName = "携程笔记";
        } else if ("review".equals(type)) {
            adType = "ctrip_review";
            platformName = "携程点评";
        } else {
            // 默认使用携程点评（兼容旧版）
            adType = "ctrip";
            platformName = "携程点评";
        }

        // 检查并扣减次数
        R deductResult = activityRechargeRecordService.checkAndDeductCount(
                null,
                activityId,
                3, // 转发类型
                null,
                 platformName + "转发"
        );

        if (!deductResult.get("code").equals(200)) {
            return deductResult;
        }

        return getReviewContent(activityId, adType, platformName);
    }

    /**
     * 重新生成抖音点评内容
     */
    @RequestMapping("/regenerate/douyin")
    public R regenerateDouyinReview(@RequestParam("activityId") Long activityId,
                                   @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "抖音点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, "douyin_review", "抖音点评", aiTag);
        } catch (Exception e) {
            return R.error("重新生成抖音点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成大众点评内容
     */
    @RequestMapping("/regenerate/dianping")
    public R regenerateDianpingReview(@RequestParam("activityId") Long activityId,
                                     @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "大众点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, "dianping", "大众点评", aiTag);
        } catch (Exception e) {
            return R.error("重新生成大众点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成美团点评内容
     */
    @RequestMapping("/regenerate/meituan")
    public R regenerateMeituanReview(@RequestParam("activityId") Long activityId,
                                    @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    "美团点评重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, "meituan", "美团点评", aiTag);
        } catch (Exception e) {
            return R.error("重新生成美团点评内容失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成携程点评内容
     */
    @RequestMapping("/regenerate/ctrip")
    public R regenerateCtripReview(@RequestParam("activityId") Long activityId,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "aiTag", required = false) String aiTag) {
        try {
            // 根据type参数决定使用哪种广告类型
            String adType;
            String platformName;

            if ("notes".equals(type)) {
                adType = "ctrip_notes";
                platformName = "携程笔记";
            } else if ("review".equals(type)) {
                adType = "ctrip_review";
                platformName = "携程点评";
            } else {
                // 默认使用携程点评（兼容旧版）
                adType = "ctrip";
                platformName = "携程点评";
            }

            // 检查并扣减次数
            R deductResult = activityRechargeRecordService.checkAndDeductCount(
                    null,
                    activityId,
                    3, // 转发类型
                    null,
                    platformName + "重新生成"
            );

            if (!deductResult.get("code").equals(200)) {
                return deductResult;
            }

            return forceGenerateNewReviewText(activityId, adType, platformName, aiTag);
        } catch (Exception e) {
            return R.error("重新生成携程点评内容失败: " + e.getMessage());
        }
    }



    /**
     * 强制生成新的点评文案（支持AI标签）
     *
     * @throws Exception
     */
    private R forceGenerateNewReviewText(Long activityId, String adType, String platformName, String aiTag) throws Exception {
        Map<String, Object> result = new HashMap<>();

        try {
            // 创建新的文案实体
            ActivityTextEntity activityText = new ActivityTextEntity();
            activityText.setActivityId(activityId);
            activityText.setAdType(adType);
            activityText.setAiTag(aiTag); // 设置AI标签

            // 强制调用AI生成新文案
            R generateResult = activityTextService.generateText(activityText, null);

            if (!generateResult.get("code").equals(200)) {
                return R.error("生成" + platformName + "文案失败: " + generateResult.get("msg"));
            }

            ActivityTextEntity newTextEntity = (ActivityTextEntity) generateResult.get("activityText");

            if (newTextEntity != null) {
                result.put("text", newTextEntity.getResult()); // 文案内容
                result.put("title", newTextEntity.getTitle()); // 提示词
                result.put("name", newTextEntity.getName()); // 标题

                // 根据AI标签筛选对应的图片
                List<ActivityImageEntity> imageList;
                if (aiTag != null && !aiTag.trim().isEmpty()) {
                    // 使用AI标签筛选图片
                    imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, aiTag, adType, 3);
                } else {
                    // 使用通用图片（ai_tag为空的图片）
                    imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, null, adType, 3);
                }

                if (!imageList.isEmpty()) {
                    result.put("images", imageList);
                    result.put("hasImages", true);
                    // 增加图片使用次数
                    imageList.forEach(e -> {
                        activityImageService.incrementUseCountByPlatform(e.getId(), adType, activityId);
                    });
                } else {
                    result.put("images", null);
                    result.put("hasImages", false);
                }
            } else {
                return R.error("生成" + platformName + "文案失败");
            }

            return R.ok().put("result", result);
        } catch (Exception e) {
            return R.error("生成" + platformName + "文案异常: " + e.getMessage());
        }
    }

    /**
     * 通用获取点评内容方法
     *
     * @throws Exception
     */
    private R getReviewContent(Long activityId, String adType, String platformName) throws Exception {
        return getReviewContent(activityId, adType, platformName, null);
    }

    /**
     * 通用获取点评内容方法（支持AI标签）
     *
     * @throws Exception
     */
    private R getReviewContent(Long activityId, String adType, String platformName, String aiTag) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 查询对应平台的文案 - 使用现有的方法
        ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeNotUse(activityId, adType);

        // 查询对应平台的图片 - 根据AI标签筛选
        List<ActivityImageEntity> imageList;
        if (aiTag != null && !aiTag.trim().isEmpty()) {
            // 使用AI标签筛选图片
            imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, aiTag, adType, 3);
        } else {
            // 使用通用图片（ai_tag为空的图片）
            imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, null, adType, 3);
        }

        // 构建返回数据
        result.put("platform", platformName);
        result.put("platformCode", adType);

        if (textEntity != null) {
            result.put("title", textEntity.getName() != null ? textEntity.getName() : textEntity.getTitle());
            result.put("content", textEntity.getResult());
            result.put("promptKeyword", textEntity.getQuery());
            result.put("textId", textEntity.getId());
            activityTextService.incrementUseCount(textEntity.getId());
        } else {
            result.put("title", "");
            result.put("content", "");
            result.put("promptKeyword", "");
            result.put("textId", null);
        }

        if (!imageList.isEmpty()) {
            result.put("images", imageList);
            result.put("hasImages", true);
            imageList.forEach(e -> {
                activityImageService.incrementUseCountByPlatform(e.getId(), adType, activityId);
            });
        } else {
            result.put("images", null);
            result.put("hasImages", false);
        }

        return R.ok().put("result", result);
    }

    /**
     * 预览点评内容方法（不消耗次数）
     *
     * @throws Exception
     */
    private R getReviewContentPreview(Long activityId, String adType, String platformName) throws Exception {
        return getReviewContentPreview(activityId, adType, platformName, null);
    }

    /**
     * 预览点评内容方法（支持AI标签，不消耗次数）
     *
     * @throws Exception
     */
    private R getReviewContentPreview(Long activityId, String adType, String platformName, String aiTag) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 如果没有传入AI标签，从活动中获取
        if (StringUtils.isBlank(aiTag)) {
            ActivityEntity activity = activityService.getById(activityId);
            if (activity != null && StringUtils.isNotEmpty(activity.getAiTag())) {
                aiTag = activity.getAiTag().split(",")[0];
            }
        }

        // 查询对应平台的文案 - 使用支持AI标签的方法，但不增加使用次数
        ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeAndAiTagNotUse(activityId, adType, aiTag);

        // 查询对应平台的图片 - 根据AI标签筛选，但不增加使用次数
        List<ActivityImageEntity> imageList;
        if (aiTag != null && !aiTag.trim().isEmpty()) {
            // 使用AI标签筛选图片
            imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, aiTag, adType, 3);
        } else {
            // 使用通用图片（ai_tag为空的图片）
            imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, null, adType, 3);
        }

        // 构建返回数据
        result.put("platform", platformName);
        result.put("platformCode", adType);

        if (textEntity != null) {
            result.put("title", textEntity.getName() != null ? textEntity.getName() : textEntity.getTitle());
            result.put("content", textEntity.getResult());
            result.put("promptKeyword", textEntity.getQuery());
            result.put("textId", textEntity.getId());
            // 预览模式不增加使用次数
        } else {
            result.put("title", "");
            result.put("content", "");
            result.put("promptKeyword", "");
            result.put("textId", null);
        }

        if (!imageList.isEmpty()) {
            result.put("images", imageList);
            result.put("hasImages", true);
            // 预览模式不增加使用次数
        } else {
            result.put("images", null);
            result.put("hasImages", false);
        }

        return R.ok().put("result", result);
    }

    /**
     * 消耗点评内容方法（确认跳转时调用）
     *
     * @throws Exception
     */
    private R consumeReviewContent(Long activityId, String adType, String platformName) throws Exception {
        return consumeReviewContent(activityId, adType, platformName, null);
    }

    /**
     * 消耗点评内容方法（支持AI标签，确认跳转时调用）
     *
     * @throws Exception
     */
    private R consumeReviewContent(Long activityId, String adType, String platformName, String aiTag) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 查询对应平台的文案
        ActivityTextEntity textEntity = activityTextService.findByActivityIdAndAdTypeNotUse(activityId, adType);

        // 查询对应平台的图片 - 根据AI标签筛选
        List<ActivityImageEntity> imageList;
        if (aiTag != null && !aiTag.trim().isEmpty()) {
            // 使用AI标签筛选图片
            imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, aiTag, adType, 3);
        } else {
            // 使用通用图片（ai_tag为空的图片）
            imageList = activityImageService.findByActivityIdAndAiTagNoUseLimitByPlatform(activityId, null, adType, 3);
        }

        // 构建返回数据
        result.put("platform", platformName);
        result.put("platformCode", adType);

        if (textEntity != null) {
            result.put("title", textEntity.getName() != null ? textEntity.getName() : textEntity.getTitle());
            result.put("content", textEntity.getResult());
            result.put("promptKeyword", textEntity.getQuery());
            result.put("textId", textEntity.getId());
            // 确认跳转时才增加使用次数
            activityTextService.incrementUseCount(textEntity.getId());
        } else {
            result.put("title", "");
            result.put("content", "");
            result.put("promptKeyword", "");
            result.put("textId", null);
        }

        if (!imageList.isEmpty()) {
            result.put("images", imageList);
            result.put("hasImages", true);
            // 确认跳转时才增加使用次数
            imageList.forEach(e -> {
                activityImageService.incrementUseCountByPlatform(e.getId(), adType, activityId);
            });
        } else {
            result.put("images", null);
            result.put("hasImages", false);
        }

        return R.ok().put("result", result);
    }

    /**
     * 消耗指定的点评内容（确认跳转时调用）
     *
     * @param activityId 活动ID
     * @param adType 广告类型
     * @param platformName 平台名称
     * @param textId 指定的文案ID
     * @param imageIds 指定的图片ID列表（逗号分隔）
     * @throws Exception
     */
    private R consumeSpecificContent(Long activityId, String adType, String platformName, Long textId, String imageIds) throws Exception {
        Map<String, Object> result = new HashMap<>();

        // 构建返回数据
        result.put("platform", platformName);
        result.put("platformCode", adType);

        // 消耗指定的文案
        if (textId != null) {
            ActivityTextEntity textEntity = activityTextService.getById(textId);
            if (textEntity != null && textEntity.getActivityId().equals(activityId)) {
                result.put("title", textEntity.getName() != null ? textEntity.getName() : textEntity.getTitle());
                result.put("content", textEntity.getResult());
                result.put("promptKeyword", textEntity.getQuery());
                result.put("textId", textEntity.getId());
                // 增加文案使用次数
                activityTextService.incrementUseCount(textEntity.getId());
            } else {
                result.put("title", "");
                result.put("content", "");
                result.put("promptKeyword", "");
                result.put("textId", null);
            }
        } else {
            result.put("title", "");
            result.put("content", "");
            result.put("promptKeyword", "");
            result.put("textId", null);
        }

        // 消耗指定的图片
        if (imageIds != null && !imageIds.trim().isEmpty()) {
            String[] imageIdArray = imageIds.split(",");
            List<ActivityImageEntity> imageList = new ArrayList<>();

            for (String imageIdStr : imageIdArray) {
                try {
                    Long imageId = Long.parseLong(imageIdStr.trim());
                    ActivityImageEntity imageEntity = activityImageService.getById(imageId);
                    if (imageEntity != null && imageEntity.getActivityId().equals(activityId)) {
                        imageList.add(imageEntity);
                        // 增加图片使用次数
                        activityImageService.incrementUseCountByPlatform(imageEntity.getId(), adType, activityId);
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效的图片ID
                    continue;
                }
            }

            if (!imageList.isEmpty()) {
                result.put("images", imageList);
                result.put("hasImages", true);
            } else {
                result.put("images", null);
                result.put("hasImages", false);
            }
        } else {
            result.put("images", null);
            result.put("hasImages", false);
        }

        return R.ok().put("result", result);
    }
}
